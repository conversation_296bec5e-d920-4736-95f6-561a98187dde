#!/usr/bin/env python3
"""
Setup script for PDF Translator
Installs required dependencies and tests the setup
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def install_requirements():
    """Install required Python packages"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found!")
        return False
    
    # Upgrade pip first
    run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip")
    
    # Install requirements
    return run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing requirements")

def test_imports():
    """Test if all required libraries can be imported"""
    print("\n🧪 Testing imports...")
    
    libraries = [
        ("pdfplumber", "PDF text extraction"),
        ("PyPDF2", "PDF processing fallback"),
        ("deep_translator", "Translation service"),
        ("reportlab", "PDF creation"),
    ]
    
    all_good = True
    for lib, description in libraries:
        try:
            __import__(lib)
            print(f"✅ {lib} - {description}")
        except ImportError:
            print(f"❌ {lib} - {description} (FAILED)")
            all_good = False
    
    return all_good

def check_pdf_file():
    """Check if the Portuguese PDF file exists"""
    pdf_files = list(Path(".").glob("*.pdf"))
    if pdf_files:
        print(f"\n📄 Found PDF files:")
        for pdf in pdf_files:
            print(f"  - {pdf}")
        return pdf_files[0]  # Return first PDF found
    else:
        print("\n⚠️  No PDF files found in current directory")
        return None

def main():
    print("🚀 Setting up PDF Translator (Portuguese to French)")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        print("\n❌ Failed to install requirements. Please check your internet connection and try again.")
        return
    
    # Test imports
    if not test_imports():
        print("\n❌ Some libraries failed to import. Please check the installation.")
        return
    
    # Check for PDF files
    pdf_file = check_pdf_file()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Place your Portuguese PDF file in this directory")
    print("2. Run the translator:")
    print("   python pdf_translator.py your_file.pdf")
    print("\n💡 Example usage:")
    if pdf_file:
        print(f"   python pdf_translator.py \"{pdf_file}\"")
    else:
        print("   python pdf_translator.py \"GMM Next Manual V1 (POR) (1)_250828_070842.pdf\"")
    print("\n🔧 Advanced options:")
    print("   python pdf_translator.py input.pdf -o output_french.pdf")
    print("   python pdf_translator.py input.pdf --source pt --target fr")

if __name__ == "__main__":
    main()
