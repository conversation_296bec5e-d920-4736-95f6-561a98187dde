@echo off
echo ========================================
echo PDF Translator: Portuguese to French
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Run setup if requirements not installed
echo Checking dependencies...
python -c "import pdfplumber" >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    python setup_translator.py
    if errorlevel 1 (
        echo Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Find PDF file
for %%f in (*.pdf) do (
    set "PDF_FILE=%%f"
    goto :found
)

echo No PDF files found in current directory
echo Please place your Portuguese PDF file here and run again
pause
exit /b 1

:found
echo Found PDF file: %PDF_FILE%
echo.
echo Starting translation...
python pdf_translator.py "%PDF_FILE%"

if errorlevel 1 (
    echo Translation failed
) else (
    echo.
    echo Translation completed successfully!
    echo Check for the translated file in this directory
)

echo.
pause
