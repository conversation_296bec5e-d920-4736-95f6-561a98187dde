#!/usr/bin/env python3
"""
PDF Translator: Portuguese to French
Extracts text from PDF, translates it, and creates a new translated PDF
"""

import os
import sys
from pathlib import Path
import argparse
from typing import Optional, List, Tuple

# PDF processing
try:
    import pdfplumber
except ImportError:
    pdfplumber = None

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

# Translation
try:
    from googletrans import Translator
except ImportError:
    Translator = None

try:
    from deep_translator import GoogleTranslator
except ImportError:
    GoogleTranslator = None

# PDF creation
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
except ImportError:
    SimpleDocTemplate = None

class PDFTranslator:
    def __init__(self, source_lang: str = 'pt', target_lang: str = 'fr'):
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.translator = None
        self._init_translator()
    
    def _init_translator(self):
        """Initialize the translation service"""
        if GoogleTranslator:
            self.translator = GoogleTranslator(source=self.source_lang, target=self.target_lang)
            print("Using deep-translator (Google)")
        elif Translator:
            self.translator = Translator()
            print("Using googletrans")
        else:
            raise ImportError("No translation library available. Install 'deep-translator' or 'googletrans'")
    
    def extract_text_pdfplumber(self, pdf_path: str) -> List[str]:
        """Extract text using pdfplumber (recommended)"""
        if not pdfplumber:
            raise ImportError("pdfplumber not available")
        
        pages_text = []
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    pages_text.append(text.strip())
                else:
                    pages_text.append("")
        return pages_text
    
    def extract_text_pypdf2(self, pdf_path: str) -> List[str]:
        """Extract text using PyPDF2 (fallback)"""
        if not PyPDF2:
            raise ImportError("PyPDF2 not available")
        
        pages_text = []
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text = page.extract_text()
                pages_text.append(text.strip() if text else "")
        return pages_text
    
    def extract_text(self, pdf_path: str) -> List[str]:
        """Extract text from PDF using available library"""
        print(f"Extracting text from: {pdf_path}")
        
        try:
            if pdfplumber:
                return self.extract_text_pdfplumber(pdf_path)
            elif PyPDF2:
                return self.extract_text_pypdf2(pdf_path)
            else:
                raise ImportError("No PDF processing library available")
        except Exception as e:
            print(f"Error extracting text: {e}")
            return []
    
    def translate_text(self, text: str) -> str:
        """Translate text from source to target language"""
        if not text.strip():
            return text
        
        try:
            if isinstance(self.translator, GoogleTranslator):
                return self.translator.translate(text)
            else:  # googletrans
                result = self.translator.translate(text, src=self.source_lang, dest=self.target_lang)
                return result.text
        except Exception as e:
            print(f"Translation error: {e}")
            return text  # Return original text if translation fails
    
    def translate_pages(self, pages_text: List[str]) -> List[str]:
        """Translate all pages"""
        translated_pages = []
        total_pages = len(pages_text)
        
        for i, page_text in enumerate(pages_text, 1):
            print(f"Translating page {i}/{total_pages}...")
            
            if not page_text.strip():
                translated_pages.append("")
                continue
            
            # Split into smaller chunks if text is very long
            chunks = self._split_text(page_text, max_length=4000)
            translated_chunks = []
            
            for chunk in chunks:
                translated_chunk = self.translate_text(chunk)
                translated_chunks.append(translated_chunk)
            
            translated_page = " ".join(translated_chunks)
            translated_pages.append(translated_page)
        
        return translated_pages
    
    def _split_text(self, text: str, max_length: int = 4000) -> List[str]:
        """Split text into smaller chunks for translation"""
        if len(text) <= max_length:
            return [text]
        
        chunks = []
        sentences = text.split('. ')
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk + sentence) <= max_length:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def create_pdf(self, translated_pages: List[str], output_path: str):
        """Create PDF with translated text"""
        if not SimpleDocTemplate:
            # Fallback: save as text file
            output_path = output_path.replace('.pdf', '.txt')
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, page_text in enumerate(translated_pages, 1):
                    f.write(f"=== PAGE {i} ===\n\n")
                    f.write(page_text)
                    f.write("\n\n" + "="*50 + "\n\n")
            print(f"Translated text saved as: {output_path}")
            return
        
        # Create PDF using reportlab
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Custom style for French text
        french_style = ParagraphStyle(
            'French',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            encoding='utf-8'
        )
        
        for i, page_text in enumerate(translated_pages, 1):
            if page_text.strip():
                # Add page header
                story.append(Paragraph(f"<b>Page {i}</b>", styles['Heading2']))
                story.append(Spacer(1, 12))
                
                # Add translated text
                # Replace problematic characters and format for PDF
                clean_text = page_text.replace('\n', '<br/>')
                story.append(Paragraph(clean_text, french_style))
                story.append(Spacer(1, 24))
        
        doc.build(story)
        print(f"Translated PDF created: {output_path}")
    
    def translate_pdf(self, input_path: str, output_path: Optional[str] = None) -> str:
        """Main method to translate PDF"""
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input PDF not found: {input_path}")
        
        if not output_path:
            base_name = Path(input_path).stem
            output_path = f"{base_name}_translated_fr.pdf"
        
        print(f"Starting translation: {input_path} -> {output_path}")
        
        # Extract text
        pages_text = self.extract_text(input_path)
        if not pages_text:
            raise ValueError("No text extracted from PDF")
        
        print(f"Extracted text from {len(pages_text)} pages")
        
        # Translate
        translated_pages = self.translate_pages(pages_text)
        
        # Create output PDF
        self.create_pdf(translated_pages, output_path)
        
        return output_path


def main():
    parser = argparse.ArgumentParser(description='Translate PDF from Portuguese to French')
    parser.add_argument('input_pdf', help='Input PDF file path')
    parser.add_argument('-o', '--output', help='Output PDF file path')
    parser.add_argument('--source', default='pt', help='Source language code (default: pt)')
    parser.add_argument('--target', default='fr', help='Target language code (default: fr)')
    
    args = parser.parse_args()
    
    try:
        translator = PDFTranslator(source_lang=args.source, target_lang=args.target)
        output_file = translator.translate_pdf(args.input_pdf, args.output)
        print(f"\n✅ Translation completed successfully!")
        print(f"📄 Output file: {output_file}")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
